package com.kaolafm.ad.api.internal.model;

import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Keep;

/**
 * 上报公共参数类，内部封装使用到的参数，不需要对外暴露。
 * <AUTHOR>
 * @date 2020-01-13
 */
@Entity
public class ReportParamEntity {

    @Id(autoincrement = true)
    private Long id;

    /**创意id, 跟服务器确认该id对于每一个广告是唯一的。*/
    private Long creativeId;

    /**广告展示后续动作需要带回的session id。每次的请求唯一，一个广告集合共用一个。*/
    private String sessionId;

    /**厂商id*/
    private long memberId;

    /** 二级厂商id，多个id用^分割*/
    private String secondaryMemberIds;

    /**媒体id*/
    private long mediaId;

    /**广告位id*/
    private long adZoneId;

    /**广告计划id*/
    private long campaignId;

    /**广告组id*/
    private long adGroupId;

    private long customerId;

    /**广告的交易类型：1:display；2:cpm*/
    private int transType;

    /**本次展示消耗的钱或者本次展示带来点击所消耗的钱，单位为分*/
    private long cost;

    /**轮播数，广告位维度的数据，再有这个广告位请求的话带回给广告引擎*/
    private int cbCarousel;

    public ReportParamEntity() {
    }






    @Keep
    public ReportParamEntity(Long id, Long creativeId, String sessionId,
            long memberId, String secondaryMemberIds, long mediaId, long adZoneId,
            long campaignId, long adGroupId, long customerId, int transType,
            long cost, int cbCarousel) {
        this.id = id;
        this.creativeId = creativeId;
        this.sessionId = sessionId;
        this.memberId = memberId;
        this.secondaryMemberIds = secondaryMemberIds;
        this.mediaId = mediaId;
        this.adZoneId = adZoneId;
        this.campaignId = campaignId;
        this.adGroupId = adGroupId;
        this.customerId = customerId;
        this.transType = transType;
        this.cost = cost;
        this.cbCarousel = cbCarousel;
    }






    public Long getCreativeId() {
        return this.creativeId;
    }

    public void setCreativeId(Long creativeId) {
        this.creativeId = creativeId;
    }

    public String getSessionId() {
        return this.sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public long getMemberId() {
        return this.memberId;
    }

    public void setMemberId(long memberId) {
        this.memberId = memberId;
    }

    public String getSecondaryMemberIds() {
        return this.secondaryMemberIds;
    }

    public void setSecondaryMemberIds(String secondaryMemberIds) {
        this.secondaryMemberIds = secondaryMemberIds;
    }

    public long getMediaId() {
        return this.mediaId;
    }

    public void setMediaId(long mediaId) {
        this.mediaId = mediaId;
    }

    public long getAdZoneId() {
        return this.adZoneId;
    }

    public void setAdZoneId(long adZoneId) {
        this.adZoneId = adZoneId;
    }

    public long getCampaignId() {
        return this.campaignId;
    }

    public void setCampaignId(long campaignId) {
        this.campaignId = campaignId;
    }

    public long getAdGroupId() {
        return this.adGroupId;
    }

    public void setAdGroupId(long adGroupId) {
        this.adGroupId = adGroupId;
    }

    public long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(long customerId) {
        this.customerId = customerId;
    }

    public int getTransType() {
        return this.transType;
    }

    public void setTransType(int transType) {
        this.transType = transType;
    }

    public long getCost() {
        return this.cost;
    }

    public void setCost(long cost) {
        this.cost = cost;
    }

    public int getCbCarousel() {
        return cbCarousel;
    }

    public void setCbCarousel(int cbCarousel) {
        this.cbCarousel = cbCarousel;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
