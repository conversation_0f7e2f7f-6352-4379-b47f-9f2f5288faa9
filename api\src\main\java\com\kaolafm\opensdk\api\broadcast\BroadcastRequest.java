package com.kaolafm.opensdk.api.broadcast;

import androidx.annotation.Nullable;

import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseRequest;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.player.model.BroadcastDateData;
import com.kaolafm.opensdk.http.core.HttpCallback;
import com.kaolafm.opensdk.http.error.ApiException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: BroadcastRequest.java
 *                                                                  *
 * Created in 2018/8/13 下午4:06
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
public class BroadcastRequest extends BaseRequest {


    private BroadcastService mService;

    public BroadcastRequest() {
        mService = obtainRetrofitService(BroadcastService.class);
    }

    /**
     * 获取广播电台详情
     *
     * @param broadcastId 在线广播ID
     * @param callback    获取数据回调
     */
    public void getBroadcastDetails(long broadcastId, HttpCallback<BroadcastDetails> callback) {
        doHttpDeal(mService.getBroadcastDetails(broadcastId), BaseResult::getResult, callback);
    }


    /**
     * 获取广播电台节目单列表
     *
     * @param broadcastId 在线广播ID
     * @param date        日期
     * @param callback    获取数据回调
     */
    public void getBroadcastProgramList(long broadcastId, @Nullable String date, HttpCallback<List<ProgramDetails>> callback) {
        doHttpDeal(mService.getBroadcastProgramList(broadcastId, date), BaseResult::getResult, callback);
    }

//    /**
//     * 获取明天电台节目单列表
//     *
//     * @param broadcastId 在线广播电台ID
//     * @param date        日期
//     * @param callback    获取数据回调
//     */
//    public void getBroadcastTomorrowProgramList(long broadcastId, @Nullable String date, HttpCallback<List<ProgramDetails>> callback) {
//        doHttpDeal(mService.getBroadcastTomorrowProgramList(broadcastId, date), BaseResult::getResult, callback);
//    }

    /**
     * 获取广播节目详情
     *
     * @param programId 广播节目ID
     * @param callback  获取数据回调
     */
    public void getBroadcastProgramDetails(long programId, HttpCallback<ProgramDetails> callback) {
        doHttpDeal(mService.getBroadcastProgramDetails(programId), BaseResult::getResult, callback);

    }

    /**
     * 获取广播电台当前节目对象
     *
     * @param broadcastId 在线广播电台ID
     * @param callback    获取数据回调
     */
    public void getBroadcastCurrentProgramDetails(long broadcastId, HttpCallback<ProgramDetails> callback) {
        doHttpDeal(mService.getBroadcastCurrentProgramDetails(broadcastId), BaseResult::getResult, callback);
    }

    /**
     * 根据广播id返回地方台或国家台广播列表
     *
     * @param broadcastId 在线广播电台ID
     * @param pagenum
     * @param pagesize
     * @param callback    获取数据回调
     */
    public void getBroadcastNeighborList(long broadcastId,int pagenum,int pagesize, HttpCallback<BasePageResult<List<BroadcastDetails>>> callback) {
        doHttpDeal(mService.getBroadcastNeighborList(broadcastId,pagenum,pagesize), BaseResult::getResult, callback);
    }

    /**
     * 获取广播日期。
     * @param callback
     */
    public void getBroadcastDate(HttpCallback<BroadcastDateData> callback) {
        doHttpDeal(mService.getBroadcastDate(), BaseResult::getResult, callback);
    }
    /**
     * 批量获取广播详情(每次最多只能获取20个广播)
     * @param List<Long> rids 批量广播id
     * @return
     */
    public void getBroadcastListDetail(List<Long> rids, HttpCallback<BroadcastInfo> callback){
        HashMap<String, Object> params = new HashMap<>();
        params.put("bids",rids);
        doHttpDeal(mService.getBroadcastListDetail(params), BaseResult::getResult, callback);
    }

    /**
     * 获取当前地区指定频率广播
     * @param freq String 批量广播id
     * @return
     */
    public void getBroadcastCity(String freq, HttpCallback<BroadcastCityDetail> callback){
        HashMap<String, Object> params = new HashMap<>();
        params.put("freq", freq);
        doHttpDeal(mService.getBroadcastCity(freq), BaseResult::getResult, callback);
    }

    /**
     * 批量获取当前地区指定频率广播
     * @param freq String 批量广播id
     * @return
     */
    public void getBroadcastCityList(String freq, HttpCallback<List<BroadcastCityDetail>> callback){
        HashMap<String, Object> params = new HashMap<>();
        params.put("freq", freq);
        doHttpDeal(mService.getBroadcastCityList(freq), BaseResult::getResult, callback);
    }

    /**
     * 获取城市在线广播列表
     * @param cityCode 城市编码
     * @param pageNum 页码
     * @param pageSize 每页数量
     */
    public void getCityBroadcastList(String cityCode, int pageNum, int pageSize, HttpCallback<BasePageResult<List<BroadcastDetails>>> callback) {
        doHttpDeal(mService.getCityBroadcastList(cityCode, pageNum, pageSize), BaseResult::getResult, callback);
    }
}
