package com.kaolafm.ad.api.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;
import com.kaolafm.opensdk.db.helper.DaoStringListConverter;

import org.greenrobot.greendao.annotation.Convert;
import org.greenrobot.greendao.annotation.Entity;
import org.greenrobot.greendao.annotation.Generated;
import org.greenrobot.greendao.annotation.Id;
import org.greenrobot.greendao.annotation.Keep;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-01-03
 */
@Entity
public class AdvertisingDetails implements Parcelable {

    /**
     * creativeId : 11
     * adType : 1
     * width : 123
     * height : 456
     * imageUrl :
     * attachImageUrl :
     * audioUrl :
     * clickMonitorUrl:
     * pvMonitorUrl :
     * jumpSeconds : 3
     * monitorType : 2
     * imageDuration : 2
     * duration : 2
     * audioDuration:
     * attachImageDuration : 2
     * adPlayTimestamp : ["2020-01-03 11:50:00"]
     * moreInteraction : 1
     * moreInteractionIcon :
     * moreInteractionType : 1
     * moreInteractionImage :
     * moreInteractionDestURL :
     * moreInteractionDisplayDuration : 1
     * moreInteractionIconDisplayOption : 1
     * moreInteractionText :
     * isJump : 1
     * moreInteractionJumpAble : 1
     * moreInteractionJumpSeconds : 1
     */

    /**
     * 创意id，每一个广告是唯一的
     * 必须是Long，因为作为数据库主键。
     */
    @Id
    @SerializedName("creativeId")
    private Long creativeId;

    /**
     * 广告位Id
     */
    @SerializedName("adzoneId")
    private int adZoneId;

    /**
     * 广告类型 1音频广告；2图片广告；3音频广告（新）；4图片广告（新）；5音图广告（新）
     */
    @SerializedName("adType")
    private int adType;

    /**
     * 广告图片宽度
     */
    @SerializedName("width")
    private int width;

    /**
     * 广告图片高度
     */
    @SerializedName("height")
    private int height;

    /**
     * 广告图片链接
     */
    @SerializedName("imageUrl")
    private String imageUrl;

    /**
     * 广告附图链接
     */
    @SerializedName("attachImageUrl")
    private String attachImageUrl;

    /**
     * 广告音频链接
     */
    @SerializedName("audioUrl")
    private String audioUrl;

    /**
     * 第三方点击监控地址
     */
    @SerializedName("clickMonitorUrl")
    private String clickMonitorUrl;

    /**
     * 第三方展示监控地址
     */
    @SerializedName("pvMonitorUrl")
    private String pvMonitorUrl;

    /**
     * 广告系统需能设置图片广告可跳过时长,单位为秒, 音频的为主
     */
    @SerializedName("jumpSeconds")
    private int jumpSeconds;

    /**
     * 监控类型：0为不监控：1为秒针；2为AdMaster；3为talkingdata
     */
    @SerializedName("monitorType")
    private int monitorType;

    /**
     * 主图显示时长，单位：秒。可以配置为0，支持只需要二次互动的广告场景
     */
    @SerializedName("imageDuration")
    private int imageDuration;

    /**
     * 音频时长。单位：秒 需提取音频播放时长经四舍五入取整后作为广告时长保存
     */
    @SerializedName("duration")
    private int duration;

    /**
     * 音频时长。（单位：秒） 需提取音频播放时长经四舍五入取整后作为广告时长保存
     */
    @SerializedName("audioDuration")
    private int audioDuration;

    /**
     * 附图显示时长（单位：秒）
     */
    @SerializedName("attachImageDuration")
    private int attachImageDuration;

    /**
     * 1，表示支持二次互动；0，表示不支持；
     */
    @SerializedName("moreInteraction")
    private int moreInteraction;

    /**
     * 二次交互Icon图片文件URL；
     */
    @SerializedName("moreInteractionIcon")
    private String moreInteractionIcon;

    /**
     * 二次交互类型：1，显示图片；2，跳转到指定URL；3，表示不支持点击
     */
    @SerializedName("moreInteractionType")
    private int moreInteractionType;

    /**
     * 二次交互点击Icon需要显示的图片；
     */
    @SerializedName("moreInteractionImage")
    private String moreInteractionImage;

    /**
     * 二次交互点击Icon跳转到的URL；
     */
    @SerializedName("moreInteractionDestURL")
    private String moreInteractionDestUrl;

    /**
     * 二次交互Icon显示时长（单位：秒），若存在正数值，按照指定时长显示，否则，一直显示到系统开始曝光下一个广告；
     */
    @SerializedName("moreInteractionDisplayDuration")
    private int moreInteractionDisplayDuration;

    /**
     * 二次互动显示时机：1，广告开始曝光时；2，广告曝光结束；
     */
    @SerializedName("moreInteractionIconDisplayOption")
    private int moreInteractionIconDisplayOption;

    /**
     * 二次互动图标伴随文字；
     */
    @SerializedName("moreInteractionText")
    private String moreInteractionText;

    /**
     * 是否可跳过：0为不跳过 1为跳过
     */
    @SerializedName("isJump")
    private int jump;

    /**
     * “yyyy-MM-dd HH24:mm:ss“，广告播放时间，服务器返回的报时广告播放时间
     */
    @SerializedName("adPlayTimestamp")
    @Convert(converter = DaoStringListConverter.class, columnType = String.class)
    private List<String> adPlayTimestamps;

    /**
     * 次级类型，手动传入
     */
    private int subtype = -1;

    /**广告展示后续动作需要带回的sessionId。每次的请求唯一，一个广告集合共用一个。用于数据上报。*/
    private String sessionId;
    /**
     * attachWidth : 123
     * attachHeight : 456
     * moreInteractionWidth :
     * moreInteractionHeight :
     */

    @SerializedName("attachWidth")
    private int attachWidth;
    @SerializedName("attachHeight")
    private int attachHeight;
    @SerializedName("moreInteractionWidth")
    private int moreInteractionWidth;
    @SerializedName("moreInteractionHeight")
    private int moreInteractionHeight;

    public AdvertisingDetails() {
    }


    public Long getCreativeId() {
        return creativeId;
    }

    public void setCreativeId(long creativeId) {
        this.creativeId = creativeId;
    }

    public int getAdType() {
        return adType;
    }

    public void setAdType(int adType) {
        this.adType = adType;
    }

    public int getWidth() {
        return width;
    }

    public void setWidth(int width) {
        this.width = width;
    }

    public int getHeight() {
        return height;
    }

    public void setHeight(int height) {
        this.height = height;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getAttachImageUrl() {
        return attachImageUrl;
    }

    public void setAttachImageUrl(String attachImageUrl) {
        this.attachImageUrl = attachImageUrl;
    }

    public String getAudioUrl() {
        return audioUrl;
    }

    public void setAudioUrl(String audioUrl) {
        this.audioUrl = audioUrl;
    }

    public String getClickMonitorUrl() {
        return clickMonitorUrl;
    }

    public void setClickMonitorUrl(String clickMonitorUrl) {
        this.clickMonitorUrl = clickMonitorUrl;
    }

    public String getPvMonitorUrl() {
        return pvMonitorUrl;
    }

    public void setPvMonitorUrl(String pvMonitorUrl) {
        this.pvMonitorUrl = pvMonitorUrl;
    }

    public int getJumpSeconds() {
        return jumpSeconds;
    }

    public void setJumpSeconds(int jumpSeconds) {
        this.jumpSeconds = jumpSeconds;
    }

    public int getMonitorType() {
        return monitorType;
    }

    public void setMonitorType(int monitorType) {
        this.monitorType = monitorType;
    }

    public int getImageDuration() {
        return imageDuration;
    }

    public void setImageDuration(int imageDuration) {
        this.imageDuration = imageDuration;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    public int getAudioDuration() {
        return audioDuration;
    }

    public void setAudioDuration(int audioDuration) {
        this.audioDuration = audioDuration;
    }

    public int getAttachImageDuration() {
        return attachImageDuration;
    }

    public void setAttachImageDuration(int attachImageDuration) {
        this.attachImageDuration = attachImageDuration;
    }

    public int getMoreInteraction() {
        return moreInteraction;
    }

    public void setMoreInteraction(int moreInteraction) {
        this.moreInteraction = moreInteraction;
    }

    public String getMoreInteractionIcon() {
        return moreInteractionIcon;
    }

    public void setMoreInteractionIcon(String moreInteractionIcon) {
        this.moreInteractionIcon = moreInteractionIcon;
    }

    public int getMoreInteractionType() {
        return moreInteractionType;
    }

    public void setMoreInteractionType(int moreInteractionType) {
        this.moreInteractionType = moreInteractionType;
    }

    public String getMoreInteractionImage() {
        return moreInteractionImage;
    }

    public void setMoreInteractionImage(String moreInteractionImage) {
        this.moreInteractionImage = moreInteractionImage;
    }

    public String getMoreInteractionDestUrl() {
        return moreInteractionDestUrl;
    }

    public void setMoreInteractionDestUrl(String moreInteractionDestUrl) {
        this.moreInteractionDestUrl = moreInteractionDestUrl;
    }

    public int getMoreInteractionDisplayDuration() {
        return moreInteractionDisplayDuration;
    }

    public void setMoreInteractionDisplayDuration(int moreInteractionDisplayDuration) {
        this.moreInteractionDisplayDuration = moreInteractionDisplayDuration;
    }

    public int getMoreInteractionIconDisplayOption() {
        return moreInteractionIconDisplayOption;
    }

    public void setMoreInteractionIconDisplayOption(int moreInteractionIconDisplayOption) {
        this.moreInteractionIconDisplayOption = moreInteractionIconDisplayOption;
    }

    public String getMoreInteractionText() {
        return moreInteractionText;
    }

    public void setMoreInteractionText(String moreInteractionText) {
        this.moreInteractionText = moreInteractionText;
    }

    public int isJump() {
        return jump;
    }

    public void setJump(int jump) {
        this.jump = jump;
    }

    public List<String> getAdPlayTimestamps() {
        return adPlayTimestamps;
    }

    public void setAdPlayTimestamps(List<String> adPlayTimestamp) {
        this.adPlayTimestamps = adPlayTimestamp;
    }

    public int getSubtype() {
        return subtype;
    }

    public void setSubtype(int subtype) {
        this.subtype = subtype;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public int getAdZoneId() {
        return this.adZoneId;
    }

    public void setAdZoneId(int adZoneId) {
        this.adZoneId = adZoneId;
    }


    public int getJump() {
        return this.jump;
    }


    public void setCreativeId(Long creativeId) {
        this.creativeId = creativeId;
    }

    @Override
    public String toString() {
        return "AdvertisingDetails{" +
                "creativeId=" + creativeId +
                ", adZoneId=" + adZoneId +
                ", adType=" + adType +
                ", width=" + width +
                ", height=" + height +
                ", imageUrl='" + imageUrl + '\'' +
                ", attachImageUrl='" + attachImageUrl + '\'' +
                ", audioUrl='" + audioUrl + '\'' +
                ", clickMonitorUrl='" + clickMonitorUrl + '\'' +
                ", pvMonitorUrl='" + pvMonitorUrl + '\'' +
                ", jumpSeconds=" + jumpSeconds +
                ", monitorType=" + monitorType +
                ", imageDuration=" + imageDuration +
                ", duration=" + duration +
                ", audioDuration=" + audioDuration +
                ", attachImageDuration=" + attachImageDuration +
                ", moreInteraction=" + moreInteraction +
                ", moreInteractionIcon='" + moreInteractionIcon + '\'' +
                ", moreInteractionType=" + moreInteractionType +
                ", moreInteractionImage='" + moreInteractionImage + '\'' +
                ", moreInteractionDestUrl='" + moreInteractionDestUrl + '\'' +
                ", moreInteractionDisplayDuration=" + moreInteractionDisplayDuration +
                ", moreInteractionIconDisplayOption=" + moreInteractionIconDisplayOption +
                ", moreInteractionText='" + moreInteractionText + '\'' +
                ", jump=" + jump +
                ", adPlayTimestamps=" + adPlayTimestamps +
                ", subtype=" + subtype +
                ", sessionId='" + sessionId + '\'' +
                '}';
    }

    public int getAttachWidth() {
        return attachWidth;
    }

    public void setAttachWidth(int attachWidth) {
        this.attachWidth = attachWidth;
    }

    public int getAttachHeight() {
        return attachHeight;
    }

    public void setAttachHeight(int attachHeight) {
        this.attachHeight = attachHeight;
    }

    public int getMoreInteractionWidth() {
        return moreInteractionWidth;
    }

    public void setMoreInteractionWidth(int moreInteractionWidth) {
        this.moreInteractionWidth = moreInteractionWidth;
    }

    public int getMoreInteractionHeight() {
        return moreInteractionHeight;
    }

    public void setMoreInteractionHeight(int moreInteractionHeight) {
        this.moreInteractionHeight = moreInteractionHeight;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeValue(this.creativeId);
        dest.writeInt(this.adZoneId);
        dest.writeInt(this.adType);
        dest.writeInt(this.width);
        dest.writeInt(this.height);
        dest.writeString(this.imageUrl);
        dest.writeString(this.attachImageUrl);
        dest.writeString(this.audioUrl);
        dest.writeString(this.clickMonitorUrl);
        dest.writeString(this.pvMonitorUrl);
        dest.writeInt(this.jumpSeconds);
        dest.writeInt(this.monitorType);
        dest.writeInt(this.imageDuration);
        dest.writeInt(this.duration);
        dest.writeInt(this.audioDuration);
        dest.writeInt(this.attachImageDuration);
        dest.writeInt(this.moreInteraction);
        dest.writeString(this.moreInteractionIcon);
        dest.writeInt(this.moreInteractionType);
        dest.writeString(this.moreInteractionImage);
        dest.writeString(this.moreInteractionDestUrl);
        dest.writeInt(this.moreInteractionDisplayDuration);
        dest.writeInt(this.moreInteractionIconDisplayOption);
        dest.writeString(this.moreInteractionText);
        dest.writeInt(this.jump);
        dest.writeStringList(this.adPlayTimestamps);
        dest.writeInt(this.subtype);
        dest.writeString(this.sessionId);
        dest.writeInt(this.attachWidth);
        dest.writeInt(this.attachHeight);
        dest.writeInt(this.moreInteractionWidth);
        dest.writeInt(this.moreInteractionHeight);
    }

    protected AdvertisingDetails(Parcel in) {
        this.creativeId = (Long) in.readValue(Long.class.getClassLoader());
        this.adZoneId = in.readInt();
        this.adType = in.readInt();
        this.width = in.readInt();
        this.height = in.readInt();
        this.imageUrl = in.readString();
        this.attachImageUrl = in.readString();
        this.audioUrl = in.readString();
        this.clickMonitorUrl = in.readString();
        this.pvMonitorUrl = in.readString();
        this.jumpSeconds = in.readInt();
        this.monitorType = in.readInt();
        this.imageDuration = in.readInt();
        this.duration = in.readInt();
        this.audioDuration = in.readInt();
        this.attachImageDuration = in.readInt();
        this.moreInteraction = in.readInt();
        this.moreInteractionIcon = in.readString();
        this.moreInteractionType = in.readInt();
        this.moreInteractionImage = in.readString();
        this.moreInteractionDestUrl = in.readString();
        this.moreInteractionDisplayDuration = in.readInt();
        this.moreInteractionIconDisplayOption = in.readInt();
        this.moreInteractionText = in.readString();
        this.jump = in.readInt();
        this.adPlayTimestamps = in.createStringArrayList();
        this.subtype = in.readInt();
        this.sessionId = in.readString();
        this.attachWidth = in.readInt();
        this.attachHeight = in.readInt();
        this.moreInteractionWidth = in.readInt();
        this.moreInteractionHeight = in.readInt();
    }


    @Keep
    public AdvertisingDetails(Long creativeId, int adZoneId, int adType, int width, int height,
            String imageUrl, String attachImageUrl, String audioUrl, String clickMonitorUrl,
            String pvMonitorUrl, int jumpSeconds, int monitorType, int imageDuration, int duration,
            int audioDuration, int attachImageDuration, int moreInteraction, String moreInteractionIcon,
            int moreInteractionType, String moreInteractionImage, String moreInteractionDestUrl,
            int moreInteractionDisplayDuration, int moreInteractionIconDisplayOption,
            String moreInteractionText, int jump, List<String> adPlayTimestamps, int subtype,
            String sessionId, int attachWidth, int attachHeight, int moreInteractionWidth,
            int moreInteractionHeight) {
        this.creativeId = creativeId;
        this.adZoneId = adZoneId;
        this.adType = adType;
        this.width = width;
        this.height = height;
        this.imageUrl = imageUrl;
        this.attachImageUrl = attachImageUrl;
        this.audioUrl = audioUrl;
        this.clickMonitorUrl = clickMonitorUrl;
        this.pvMonitorUrl = pvMonitorUrl;
        this.jumpSeconds = jumpSeconds;
        this.monitorType = monitorType;
        this.imageDuration = imageDuration;
        this.duration = duration;
        this.audioDuration = audioDuration;
        this.attachImageDuration = attachImageDuration;
        this.moreInteraction = moreInteraction;
        this.moreInteractionIcon = moreInteractionIcon;
        this.moreInteractionType = moreInteractionType;
        this.moreInteractionImage = moreInteractionImage;
        this.moreInteractionDestUrl = moreInteractionDestUrl;
        this.moreInteractionDisplayDuration = moreInteractionDisplayDuration;
        this.moreInteractionIconDisplayOption = moreInteractionIconDisplayOption;
        this.moreInteractionText = moreInteractionText;
        this.jump = jump;
        this.adPlayTimestamps = adPlayTimestamps;
        this.subtype = subtype;
        this.sessionId = sessionId;
        this.attachWidth = attachWidth;
        this.attachHeight = attachHeight;
        this.moreInteractionWidth = moreInteractionWidth;
        this.moreInteractionHeight = moreInteractionHeight;
    }

    public static final Creator<AdvertisingDetails> CREATOR = new Creator<AdvertisingDetails>() {
        @Override
        public AdvertisingDetails createFromParcel(Parcel source) {
            return new AdvertisingDetails(source);
        }

        @Override
        public AdvertisingDetails[] newArray(int size) {
            return new AdvertisingDetails[size];
        }
    };
}
