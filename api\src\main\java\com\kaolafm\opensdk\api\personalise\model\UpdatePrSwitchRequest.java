package com.kaolafm.opensdk.api.personalise.model;

import com.google.gson.annotations.SerializedName;

/**
 * 更新个性化推荐开关请求体
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public class UpdatePrSwitchRequest {

    /**
     * 开关状态：1-开启，0-关闭
     */
    @SerializedName("status")
    private int status;

    public UpdatePrSwitchRequest() {
    }

    public UpdatePrSwitchRequest(int status) {
        this.status = status;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "UpdatePrSwitchRequest{" +
                "status=" + status +
                '}';
    }
}
