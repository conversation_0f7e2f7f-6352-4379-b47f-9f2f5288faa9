package com.kaolafm.opensdk.api.personalise.model;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.SerializedName;

/**
 * 个性化推荐开关
 * 
 * <AUTHOR>
 * @date 2025-08-18
 */
public class PersonalizedRecommendSwitch implements Parcelable {

    /**
     * 主键ID
     */
    @SerializedName("id")
    private int id;

    /**
     * 设备ID
     */
    @SerializedName("deviceId")
    private String deviceId;

    /**
     * 用户ID
     */
    @SerializedName("uid")
    private String uid;

    /**
     * 开关状态：1-开启，0-关闭
     */
    @SerializedName("status")
    private int status;

    public PersonalizedRecommendSwitch() {
    }

    protected PersonalizedRecommendSwitch(Parcel in) {
        id = in.readInt();
        deviceId = in.readString();
        uid = in.readString();
        status = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeInt(id);
        dest.writeString(deviceId);
        dest.writeString(uid);
        dest.writeInt(status);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<PersonalizedRecommendSwitch> CREATOR = new Creator<PersonalizedRecommendSwitch>() {
        @Override
        public PersonalizedRecommendSwitch createFromParcel(Parcel in) {
            return new PersonalizedRecommendSwitch(in);
        }

        @Override
        public PersonalizedRecommendSwitch[] newArray(int size) {
            return new PersonalizedRecommendSwitch[size];
        }
    };

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "PersonalizedRecommendSwitch{" +
                "id=" + id +
                ", deviceId='" + deviceId + '\'' +
                ", uid='" + uid + '\'' +
                ", status=" + status +
                '}';
    }
}
