package com.kaolafm.opensdk.api.broadcast;

import com.kaolafm.opensdk.api.ApiHostConstants;
import com.kaolafm.opensdk.api.BasePageResult;
import com.kaolafm.opensdk.api.BaseResult;
import com.kaolafm.opensdk.api.KaolaApiConstant;
import com.kaolafm.opensdk.api.player.model.BroadcastDateData;

import java.util.List;
import java.util.Map;

import io.reactivex.Single;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.Headers;
import retrofit2.http.POST;
import retrofit2.http.Query;

/********************************************************************
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * Copyright (C) 2000-2100, by KaolaFm, All rights reserved.        
 *                                                                  *
 * -----------------------------------------------------------------
 *                                                                  *
 * File: BroadcastService.java                                               
 *                                                                  *
 * Created in 2018/8/13 下午4:06                                       
 *                                                                  *
 * <AUTHOR>
 *                                                                  *
 * @version 1.0
 *                                                                  *
 * @since 1.0
 *                                                                  *
 ********************************************************************/
/*package*/ interface BroadcastService {

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_LIST)
    Single<BaseResult<BasePageResult<List<BroadcastDetails>>>> getBroadcastList(@Query("type") int type, @Query("classifyid") int classifyId, @Query("pagenum") int pageNum, @Query("pagesize") int pageSize, @Query("area") int area);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_NEIGHBOR_LIST)
    Single<BaseResult<BasePageResult<List<BroadcastDetails>>>> getBroadcastNeighborList(@Query("id") long id, @Query("pagenum") int pagenum, @Query("pagesize") int pagesize);


    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_DETAILS)
    Single<BaseResult<BroadcastDetails>> getBroadcastDetails(@Query("bid") long broadcastId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_PROGRAM_LIST)
    Single<BaseResult<List<ProgramDetails>>> getBroadcastProgramList(@Query("bid") long broadcastId, @Query("date") String date);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_DETAILS_TOMORROW)
    Single<BaseResult<List<ProgramDetails>>> getBroadcastTomorrowProgramList(@Query("bid") long broadcastId, @Query("date") String date);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_PROGRAM_DETAILS)
    Single<BaseResult<ProgramDetails>> getBroadcastProgramDetails(@Query("programid") long programId);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_CURRENT_PROGRAM)
    Single<BaseResult<ProgramDetails>> getBroadcastCurrentProgramDetails(@Query("bid") long broadcastId);


    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_AREA_LIST)
    Single<BaseResult<List<BroadcastArea>>> getBroadcastAreaList();

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_AREA)
    Single<BaseResult<BroadcastArea>> getBroadcastArea(@Query("lon") float longitude, @Query("lat") float latitude);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_DATE)
    Single<BaseResult<BroadcastDateData>> getBroadcastDate();

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @POST(KaolaApiConstant.GET_BROADCAST_LIST_DETAIL)
    Single<BaseResult<BroadcastInfo>> getBroadcastListDetail(@Body Map<String, Object> params);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_CITY)
    Single<BaseResult<BroadcastCityDetail>> getBroadcastCity(@Query("freq") String freq);

    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_BROADCAST_CITY_LIST)
    Single<BaseResult<List<BroadcastCityDetail>>> getBroadcastCityList(@Query("freq") String freq);

    /**
     * 城市在线广播列表
     */
    @Headers(ApiHostConstants.OPEN_KAOLA_DOMAIN_HEADER)
    @GET(KaolaApiConstant.GET_CITY_BROADCAST_LIST)
    Single<BaseResult<BasePageResult<List<BroadcastDetails>>>> getCityBroadcastList(
            @Query("citycode") String cityCode,
            @Query("pagenum") int pageNum,
            @Query("pagesize") int pageSize
    );
}
